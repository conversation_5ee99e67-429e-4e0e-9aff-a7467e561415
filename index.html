<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>

    <!-- External CSS Files -->
    <link rel="stylesheet" href="/public/css/variables.css?v=4">
    <link rel="stylesheet" href="/public/css/main.css?v=3">
    <link rel="stylesheet" href="/public/css/glassmorphism.css?v=1">
    <link rel="stylesheet" href="/public/css/advanced-features.css?v=1">
    <link rel="stylesheet" href="/public/css/modern-animations.css?v=1">
    <link rel="stylesheet" href="/public/css/preferences.css?v=2">
    <link rel="stylesheet" href="/public/css/responsive.css?v=2">
    <link rel="stylesheet" href="/public/css/posts.css?v=2">
    <link rel="stylesheet" href="/public/css/forms.css?v=2">
    <link rel="stylesheet" href="/public/css/notifications.css?v=2">
    <link rel="stylesheet" href="/public/css/performance.css?v=3">
    <link rel="stylesheet" href="/public/css/scroll-sections.css?v=2">
    <link rel="stylesheet" href="/public/css/layout-improvements.css?v=1">
    <link rel="stylesheet" href="/public/css/stories-fix.css?v=1">
    <link rel="stylesheet" href="/public/css/index-specific.css?v=1">

    <!-- Google Fonts for Modern Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <header class="header header-glass">
        <div class="container">
            <div class="header-content">
                <div class="logo glow-primary">Naroop</div>
                <div class="search-container search-modern">
                    <div class="search-icon" aria-hidden="true">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                    </div>
                    <label for="search-input" class="sr-only">Search communities, topics, and people</label>
                    <input type="text" id="search-input" class="search-input"
                           placeholder="Search communities, topics, people..."
                           aria-label="Search communities, topics, and people">
                </div>
                <div class="user-actions">
                    <button class="preferences-btn btn-glass" id="preferencesBtn" aria-label="Open accessibility preferences">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m17-4a4 4 0 01-8 0 4 4 0 018 0zM7 21a4 4 0 01-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </button>
                    <button class="notification-btn btn-glass" aria-label="View notifications">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                            <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                        </svg>
                    </button>
                    <div class="profile-img glass-card" role="button" tabindex="0" aria-label="User profile menu">G</div>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="main-content" id="main-content">
            <aside class="sidebar glass-card slide-in-left">
                <h3>Navigation</h3>
                <nav role="navigation" aria-label="Main navigation" class="stagger-children">
                    <div class="nav-item nav-item-modern active focus-ring ripple-effect" data-section="feed" aria-current="page" role="button" tabindex="0" aria-label="Feed - Current page">
                        <div class="nav-icon" aria-hidden="true">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                        </div>
                        <span>Feed</span>
                    </div>
                    <div class="nav-item nav-item-modern focus-ring ripple-effect" data-section="explore" role="button" tabindex="0" aria-label="Explore content">
                        <div class="nav-icon" aria-hidden="true">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polygon points="16.24,7.76 14.12,14.12 7.76,16.24 9.88,9.88 16.24,7.76"></polygon>
                            </svg>
                        </div>
                        <span>Explore</span>
                    </div>
                    <div class="nav-item nav-item-modern focus-ring ripple-effect" data-section="messages" role="button" tabindex="0" aria-label="Messages">
                        <div class="nav-icon" aria-hidden="true">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                            </svg>
                        </div>
                        <span>Messages</span>
                    </div>
                    <div class="nav-item nav-item-modern focus-ring ripple-effect" data-section="profile" role="button" tabindex="0" aria-label="Profile">
                        <div class="nav-icon" aria-hidden="true">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </div>
                        <span>Profile</span>
                    </div>
                    <div class="nav-item nav-item-modern focus-ring ripple-effect" data-section="communities" role="button" tabindex="0" aria-label="Communities">
                        <div class="nav-icon" aria-hidden="true">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                <circle cx="9" cy="7" r="4"></circle>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                            </svg>
                        </div>
                        <span>Communities</span>
                    </div>
                    <div class="nav-item nav-item-modern focus-ring ripple-effect" data-section="analytics" role="button" tabindex="0" aria-label="Analytics">
                        <div class="nav-icon" aria-hidden="true">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="20" x2="18" y2="10"></line>
                                <line x1="12" y1="20" x2="12" y2="4"></line>
                                <line x1="6" y1="20" x2="6" y2="14"></line>
                            </svg>
                        </div>
                        <span>Analytics</span>
                    </div>
                </nav>
            </aside>

            <!-- Stories Section - Separate from feed -->
            <section class="stories-section content-section" id="stories-section">
                <div class="stories-container">
                    <div class="story-highlights-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px; width: 100%;">
                        <h4 style="margin: 0; color: var(--text-primary); font-weight: var(--font-weight-semibold);">Stories</h4>
                        <button class="btn-glass" style="padding: 6px 12px; font-size: var(--font-size-sm);">View All</button>
                    </div>
                    <div class="story-highlights-container" style="display: flex; gap: 12px; overflow-x: auto; padding-bottom: 4px;">
                            <!-- Add Story Button -->
                            <div class="story-item add-story" style="flex-shrink: 0; display: flex; flex-direction: column; align-items: center; gap: 8px; cursor: pointer;">
                                <div class="story-avatar" style="width: 64px; height: 64px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; position: relative; border: 2px solid transparent;">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                </div>
                                <span style="font-size: var(--font-size-xs); color: var(--text-secondary); text-align: center;">Add Story</span>
                            </div>

                            <!-- Sample Story Items -->
                            <div class="story-item" style="flex-shrink: 0; display: flex; flex-direction: column; align-items: center; gap: 8px; cursor: pointer;">
                                <div class="story-avatar" style="width: 64px; height: 64px; border-radius: 50%; background: linear-gradient(45deg, var(--accent-color), var(--secondary-color)); display: flex; align-items: center; justify-content: center; position: relative; border: 3px solid var(--accent-color); padding: 2px;">
                                    <div style="width: 100%; height: 100%; border-radius: 50%; background: var(--surface-color); display: flex; align-items: center; justify-content: center; font-weight: bold; color: var(--text-primary);">M</div>
                                </div>
                                <span style="font-size: var(--font-size-xs); color: var(--text-secondary); text-align: center;">Maya</span>
                            </div>

                            <div class="story-item" style="flex-shrink: 0; display: flex; flex-direction: column; align-items: center; gap: 8px; cursor: pointer;">
                                <div class="story-avatar" style="width: 64px; height: 64px; border-radius: 50%; background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); display: flex; align-items: center; justify-content: center; position: relative; border: 3px solid var(--primary-color); padding: 2px;">
                                    <div style="width: 100%; height: 100%; border-radius: 50%; background: var(--surface-color); display: flex; align-items: center; justify-content: center; font-weight: bold; color: var(--text-primary);">J</div>
                                </div>
                                <span style="font-size: var(--font-size-xs); color: var(--text-secondary); text-align: center;">Jordan</span>
                            </div>

                            <div class="story-item" style="flex-shrink: 0; display: flex; flex-direction: column; align-items: center; gap: 8px; cursor: pointer;">
                                <div class="story-avatar" style="width: 64px; height: 64px; border-radius: 50%; background: linear-gradient(45deg, var(--secondary-color), var(--accent-color)); display: flex; align-items: center; justify-content: center; position: relative; border: 3px solid var(--secondary-color); padding: 2px;">
                                    <div style="width: 100%; height: 100%; border-radius: 50%; background: var(--surface-color); display: flex; align-items: center; justify-content: center; font-weight: bold; color: var(--text-primary);">A</div>
                                </div>
                                <span style="font-size: var(--font-size-xs); color: var(--text-secondary); text-align: center;">Amara</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Feed Section -->
            <main class="feed content-section active" id="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <div class="story-prompt card-modern smooth-hover">
                        <h4>
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: inline; margin-right: 8px;">
                                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
                            </svg>
                            Share Your Story
                        </h4>
                        <p>What positive experience would you like to share with the community today?</p>
                        <button class="create-post-btn btn-glass ripple-effect hover-lift focus-ring" id="createPostBtn" onclick="addBounceEffect(this)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            Create Post
                        </button>
                    </div>
                </div>
                <div class="feed-content">
                    <!-- Loading State (hidden by default) -->
                    <div class="loading-state" id="feedLoading" style="display: none;">
                        <div class="loading-skeleton card"></div>
                        <div class="loading-skeleton card"></div>
                        <div class="loading-skeleton card"></div>
                    </div>

                    <!-- Empty State -->
                    <div class="empty-state" id="feedEmpty">
                        <h4>Welcome to your feed!</h4>
                        <p>Start by creating your first post or connecting with other community members. Your positive stories and experiences will appear here.</p>
                    </div>

                    <!-- Posts Container -->
                    <div id="postsContainer">
                        <!-- Sample Enhanced Feed Cards -->
                        <article class="feed-card">
                            <div class="feed-card-header">
                                <div class="feed-card-avatar">M</div>
                                <div class="feed-card-user-info">
                                    <h4 class="feed-card-username">Maya Johnson</h4>
                                    <p class="feed-card-timestamp">2 hours ago</p>
                                </div>
                                <button class="btn-glass" style="padding: 6px 12px; font-size: var(--font-size-sm);">Follow</button>
                            </div>
                            <div class="feed-card-content">
                                <p>Just graduated from medical school! 🎓 The journey wasn't easy, but the support from my community kept me going. Representation matters, and I'm excited to serve and inspire the next generation. #BlackExcellence #MedicalSchool #DreamsComeTrue</p>
                            </div>
                            <div class="feed-card-actions">
                                <button class="feed-action-btn">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                                    </svg>
                                    <span>124</span>
                                </button>
                                <button class="feed-action-btn">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                    </svg>
                                    <span>18</span>
                                </button>
                                <button class="feed-action-btn">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="18" cy="5" r="3"></circle>
                                        <circle cx="6" cy="12" r="3"></circle>
                                        <circle cx="18" cy="19" r="3"></circle>
                                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                                    </svg>
                                    <span>Share</span>
                                </button>
                            </div>
                        </article>

                        <article class="feed-card">
                            <div class="feed-card-header">
                                <div class="feed-card-avatar">J</div>
                                <div class="feed-card-user-info">
                                    <h4 class="feed-card-username">Jordan Williams</h4>
                                    <p class="feed-card-timestamp">5 hours ago</p>
                                </div>
                                <button class="btn-glass" style="padding: 6px 12px; font-size: var(--font-size-sm);">Follow</button>
                            </div>
                            <div class="feed-card-content">
                                <p>Opened my first restaurant today! 🍽️ From cooking in my grandmother's kitchen to serving the community with authentic flavors and love. Thank you to everyone who believed in this dream. #BlackBusiness #CommunitySupport #DreamBig</p>
                            </div>
                            <div class="feed-card-actions">
                                <button class="feed-action-btn liked">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2">
                                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                                    </svg>
                                    <span>89</span>
                                </button>
                                <button class="feed-action-btn">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                    </svg>
                                    <span>12</span>
                                </button>
                                <button class="feed-action-btn">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="18" cy="5" r="3"></circle>
                                        <circle cx="6" cy="12" r="3"></circle>
                                        <circle cx="18" cy="19" r="3"></circle>
                                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                                    </svg>
                                    <span>Share</span>
                                </button>
                            </div>
                        </article>
                    </div>

                    <!-- Load More Section - Modern Social Media Style -->
                    <div class="load-more-container">
                        <button class="load-more-btn" id="loadMoreBtn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                                <path d="M12 5v14M5 12h14"></path>
                            </svg>
                            Load More Posts
                        </button>
                    </div>

                    <!-- Loading Indicator for Infinite Scroll -->
                    <div class="loading-indicator" id="scrollLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        <span style="margin-left: 12px;">Loading more posts...</span>
                    </div>
                </div>
            </main>

            <!-- Explore Section -->
            <section class="feed content-section" id="explore-section">
                <div class="feed-header">
                    <h2 class="feed-title">Explore</h2>
                </div>
                <div class="feed-content">
                    <div class="explore-content">
                        <h3><span aria-hidden="true">🔍</span> Discover New Content</h3>
                        <p>Explore trending posts and discover new voices in the community.</p>
                        <div class="explore-categories">
                            <div class="category-tag">#Trending</div>
                            <div class="category-tag">#BlackExcellence</div>
                            <div class="category-tag">#CommunityLove</div>
                            <div class="category-tag">#Inspiration</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Trending Topics Section -->
            <section class="feed content-section" id="trending-section">
                <div class="feed-header">
                    <h2 class="feed-title">Trending Topics</h2>
                </div>
                <div class="feed-content">
                    <div class="trending-topic-detail" id="trending-detail-container">
                        <div class="empty-state">
                            <div class="empty-state-icon" aria-hidden="true">📈</div>
                            <h3>No trending topics yet</h3>
                            <p>Trending topics will appear here as the community grows and creates engaging content.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="feed content-section" id="messages-section">
                <div class="feed-header">
                    <h2 class="feed-title">Messages</h2>
                </div>
                <div class="feed-content">
                    <div class="messages-content">
                        <h3><span aria-hidden="true">💬</span> Your Messages</h3>
                        <p>Connect and communicate with your community.</p>
                        <div class="messages-placeholder">
                            <p>No messages yet. Start a conversation!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section class="feed content-section" id="profile-section">
                <div class="feed-header">
                    <h2 class="feed-title">Profile</h2>
                </div>
                <div class="feed-content">
                    <div class="profile-content">
                        <div class="profile-header">
                            <div class="profile-avatar">👤</div>
                            <div class="profile-info">
                                <h3 id="profileUsername">Guest User</h3>
                                <p id="profileEmail">Sign in to view your profile</p>
                            </div>
                        </div>
                        <div class="profile-stats">
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Posts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Followers</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Following</span>
                            </div>
                        </div>
                        <div class="profile-actions">
                            <button class="nav-btn" id="signOutBtn">Sign Out</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Communities Section -->
            <section class="feed content-section" id="communities-section">
                <div class="feed-header">
                    <h2 class="feed-title">Communities</h2>
                </div>
                <div class="feed-content">
                    <div class="communities-content">
                        <h3><span aria-hidden="true">🎯</span> Join Communities</h3>
                        <p>Connect with like-minded people and join communities that inspire you.</p>
                        <div class="communities-grid" id="communities-container">
                            <div class="empty-state">
                                <div class="empty-state-icon" aria-hidden="true">👥</div>
                                <h4>No communities yet</h4>
                                <p>Communities will be created as the platform grows. Be the first to start meaningful conversations!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section class="feed content-section" id="analytics-section">
                <div class="feed-header">
                    <h2 class="feed-title">Analytics</h2>
                </div>
                <div class="feed-content">
                    <div class="analytics-content">
                        <h3><span aria-hidden="true">📊</span> Your Analytics</h3>
                        <p>Track your engagement and community impact.</p>
                        <div class="analytics-grid" id="analytics-container">
                            <div class="empty-state">
                                <div class="empty-state-icon" aria-hidden="true">📊</div>
                                <h4>No analytics data yet</h4>
                                <p>Start posting and engaging with the community to see your analytics here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <aside class="trending glass-card slide-in-right">
                <h3>Trending Topics</h3>
                <div id="trending-container" class="fade-in-up">
                    <div class="empty-state">
                        <div class="empty-state-icon" aria-hidden="true">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"></polyline>
                                <polyline points="17,6 23,6 23,12"></polyline>
                            </svg>
                        </div>
                        <h4>No trending topics yet</h4>
                        <p>Trending topics will appear here as the community grows and engages with content.</p>
                    </div>
                </div>
            </aside>
        </div>
    </div>

    <!-- Mobile Navigation Bar -->
    <nav class="mobile-nav glass-light" role="navigation" aria-label="Mobile navigation">
        <a href="#" class="mobile-nav-item active ripple-effect" data-section="feed" aria-label="Feed">
            <div class="mobile-nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                    <line x1="8" y1="21" x2="16" y2="21"></line>
                    <line x1="12" y1="17" x2="12" y2="21"></line>
                </svg>
            </div>
            <span>Feed</span>
        </a>
        <a href="#" class="mobile-nav-item ripple-effect" data-section="explore" aria-label="Explore">
            <div class="mobile-nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polygon points="16.24,7.76 14.12,14.12 7.76,16.24 9.88,9.88 16.24,7.76"></polygon>
                </svg>
            </div>
            <span>Explore</span>
        </a>
        <a href="#" class="mobile-nav-item ripple-effect" data-section="messages" aria-label="Messages">
            <div class="mobile-nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
            </div>
            <span>Messages</span>
        </a>
        <a href="#" class="mobile-nav-item ripple-effect" data-section="profile" aria-label="Profile">
            <div class="mobile-nav-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
            </div>
            <span>Profile</span>
        </a>
    </nav>

    <!-- Accessibility Preferences Panel -->
    <div class="preferences-panel" id="preferencesPanel" role="dialog" aria-labelledby="preferences-title" aria-hidden="true">
        <div class="preferences-content">
            <h3 id="preferences-title">Accessibility Preferences</h3>

            <div class="preference-group">
                <h4>Visual Preferences</h4>
                <div class="toggle-switch">
                    <label for="high-contrast">High Contrast Mode</label>
                    <input type="checkbox" id="high-contrast" aria-describedby="high-contrast-desc">
                </div>
                <p id="high-contrast-desc" class="preference-description">Increases contrast for better visibility</p>

                <div class="toggle-switch">
                    <label for="large-text">Large Text</label>
                    <input type="checkbox" id="large-text" aria-describedby="large-text-desc">
                </div>
                <p id="large-text-desc" class="preference-description">Increases font size for better readability</p>
            </div>

            <div class="preference-group">
                <h4>Motion Preferences</h4>
                <div class="toggle-switch">
                    <label for="reduce-motion">Reduce Motion</label>
                    <input type="checkbox" id="reduce-motion" aria-describedby="reduce-motion-desc">
                </div>
                <p id="reduce-motion-desc" class="preference-description">Minimizes animations and transitions</p>
            </div>

            <div class="preference-group">
                <h4>Keyboard Navigation</h4>
                <div class="toggle-switch">
                    <label for="enhanced-focus">Enhanced Focus Indicators</label>
                    <input type="checkbox" id="enhanced-focus" aria-describedby="enhanced-focus-desc">
                </div>
                <p id="enhanced-focus-desc" class="preference-description">Makes focus indicators more visible</p>
            </div>

            <button class="btn-base btn-primary btn-md" onclick="closePreferences()" style="width: 100%; margin-top: var(--spacing-lg);">
                Close Preferences
            </button>
        </div>
    </div>











    <!-- Include essential JavaScript files for functionality -->
    <script type="module" src="/public/js/utils.js"></script>
    <script type="module" src="/public/js/notification-system.js"></script>
    <script type="module" src="/public/js/firebase-config.js"></script>
    <script type="module" src="/public/js/authentication.js"></script>
    <script type="module" src="/public/js/core.js"></script>
    <script type="module" src="/public/js/navigation.js"></script>
    <script type="module" src="/public/js/posts.js"></script>
    <script type="module" src="/public/js/profile.js"></script>
    <script src="/public/js/modal-system.js"></script>
    <script src="/public/js/accessibility-preferences.js"></script>

    <script type="module">
    // Import notification system
    import notificationSystem from '/public/js/notification-system.js';

    // Notification button handler
    document.addEventListener('DOMContentLoaded', function() {
      var notificationBtn = document.querySelector('.notification-btn');
      if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
          notificationSystem.info('No new notifications yet.');
        });
      }

      // Note: Community and post interaction handlers are now managed by the Posts module
    });

    // Micro-interactions and UI enhancements
    function addBounceEffect(element) {
        if (!document.body.classList.contains('reduce-motion-mode')) {
            element.classList.add('bounce-on-click');
            setTimeout(() => {
                element.classList.remove('bounce-on-click');
            }, 600);
        }
    }

    // Show loading state
    function showLoadingState() {
        const loading = document.getElementById('feedLoading');
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (loading) loading.style.display = 'block';
        if (empty) empty.style.display = 'none';
        if (container) container.style.display = 'none';
    }

    // Hide loading state
    function hideLoadingState() {
        const loading = document.getElementById('feedLoading');
        if (loading) loading.style.display = 'none';
    }

    // Show empty state
    function showEmptyState() {
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (empty) empty.style.display = 'block';
        if (container) container.style.display = 'none';
    }

    // Show posts container
    function showPostsContainer() {
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (empty) empty.style.display = 'none';
        if (container) {
            container.style.display = 'block';
            container.classList.add('fade-in-up');
        }
    }

    // Add notification pulse effect
    function addNotificationPulse() {
        const notificationBtn = document.querySelector('.notification-btn');
        if (notificationBtn && !document.body.classList.contains('reduce-motion-mode')) {
            notificationBtn.classList.add('notification-pulse');
            setTimeout(() => {
                notificationBtn.classList.remove('notification-pulse');
            }, 2000);
        }
    }

    // Enhanced keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Handle Enter key on elements with tabindex
        if (e.key === 'Enter') {
            const target = e.target;
            if (target.hasAttribute('tabindex') && target.getAttribute('role') === 'button') {
                target.click();
            }
        }

        // Handle arrow key navigation in sidebar
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            const navItems = document.querySelectorAll('.nav-item[tabindex="0"]');
            const currentIndex = Array.from(navItems).indexOf(document.activeElement);

            if (currentIndex !== -1) {
                e.preventDefault();
                let nextIndex;

                if (e.key === 'ArrowDown') {
                    nextIndex = (currentIndex + 1) % navItems.length;
                } else {
                    nextIndex = (currentIndex - 1 + navItems.length) % navItems.length;
                }

                navItems[nextIndex].focus();
            }
        }
    });

    // Enhanced UI interactions and animations
    document.addEventListener('DOMContentLoaded', function() {
        // Add fade-in animation to main content
        const mainContent = document.querySelector('.main-content');
        if (mainContent && !document.body.classList.contains('reduce-motion-mode')) {
            mainContent.classList.add('fade-in-up');
        }

        // Initialize all modern features
        initializeMobileNavigation();
        initializeEnhancedInteractions();
        initializeRippleEffects();
        initializeNavigationEnhancements();
        initializeStoryHighlights();
        initializePostCreationModal();
        initializeFeedInteractions();

        // Add pulse effect to create post button
        const createPostBtn = document.getElementById('createPostBtn');
        if (createPostBtn) {
            createPostBtn.classList.add('pulse-glow');
        }

        // Show sample posts instead of empty state
        const emptyState = document.getElementById('feedEmpty');
        const postsContainer = document.getElementById('postsContainer');
        if (emptyState && postsContainer) {
            emptyState.style.display = 'none';
            postsContainer.style.display = 'block';
        }

        // Simulate loading state for demonstration
        setTimeout(() => {
            hideLoadingState();
            showEmptyState();
        }, 1000);
    });

    // Mobile navigation functionality
    function initializeMobileNavigation() {
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
        const desktopNavItems = document.querySelectorAll('.nav-item');

        mobileNavItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const section = this.dataset.section;

                // Update mobile nav active state
                mobileNavItems.forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');

                // Update desktop nav active state
                desktopNavItems.forEach(nav => nav.classList.remove('active'));
                const correspondingDesktopNav = document.querySelector(`.nav-item[data-section="${section}"]`);
                if (correspondingDesktopNav) {
                    correspondingDesktopNav.classList.add('active');
                }

                // Trigger section change
                if (window.NavigationManager && window.NavigationManager.showSection) {
                    window.NavigationManager.showSection(section);
                }
            });
        });
    }

    // Enhanced interactions
    function initializeEnhancedInteractions() {
        // Add ripple effect to buttons
        const buttons = document.querySelectorAll('.btn-base');
        buttons.forEach(button => {
            button.addEventListener('click', createRippleEffect);
        });

        // Add hover sound feedback (optional)
        const interactiveElements = document.querySelectorAll('.nav-item, .btn-base, .card-base');
        interactiveElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                if (!document.body.classList.contains('reduce-motion-mode')) {
                    this.style.transform = 'translateY(-1px)';
                }
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);

        // Observe cards and sections
        const observeElements = document.querySelectorAll('.card-base, .content-section');
        observeElements.forEach(el => observer.observe(el));
    }

    // Enhanced ripple effect for buttons
    function createRippleEffect(e) {
        if (document.body.classList.contains('reduce-motion-mode')) return;

        const button = e.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // Add ripple effect to all interactive elements
    function initializeRippleEffects() {
        const rippleElements = document.querySelectorAll('.ripple-effect');
        rippleElements.forEach(element => {
            element.addEventListener('click', createRippleEffect);
        });
    }

    // Enhanced navigation interactions
    function initializeNavigationEnhancements() {
        const navItems = document.querySelectorAll('.nav-item-modern');
        navItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(4px)';
                }
            });

            item.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = '';
                }
            });
        });

        // Mobile nav enhancements
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
        mobileNavItems.forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.transform = 'translateY(-2px) scale(0.98)';
            });

            item.addEventListener('touchend', function() {
                this.style.transform = '';
            });
        });
    }

    // Story highlights interactions
    function initializeStoryHighlights() {
        const storyItems = document.querySelectorAll('.story-item');
        storyItems.forEach(item => {
            item.addEventListener('click', function() {
                if (this.classList.contains('add-story')) {
                    // Handle add story action
                    console.log('Add story clicked');
                    // You can add modal or navigation logic here
                } else {
                    // Handle view story action
                    console.log('View story clicked');
                    // You can add story viewer logic here
                }
            });
        });
    }

    // Enhanced Post Creation Modal
    function initializePostCreationModal() {
        const fabBtn = document.getElementById('fabCreatePost');
        const createPostBtn = document.getElementById('createPostBtn');
        const modal = document.getElementById('postCreationModal');
        const closeBtn = document.getElementById('closePostModal');
        const mediaUploadArea = document.getElementById('mediaUploadArea');
        const mediaInput = document.getElementById('mediaInput');
        const hashtagInput = document.getElementById('hashtagInput');
        const hashtagDropdown = document.getElementById('hashtagDropdown');

        // Open modal
        function openModal() {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // Close modal
        function closeModal() {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Event listeners
        if (fabBtn) fabBtn.addEventListener('click', openModal);
        if (createPostBtn) createPostBtn.addEventListener('click', openModal);
        if (closeBtn) closeBtn.addEventListener('click', closeModal);

        // Close on backdrop click
        modal.addEventListener('click', function(e) {
            if (e.target === modal) closeModal();
        });

        // Media upload functionality
        if (mediaUploadArea && mediaInput) {
            mediaUploadArea.addEventListener('click', () => mediaInput.click());

            mediaUploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            mediaUploadArea.addEventListener('dragleave', function() {
                this.classList.remove('dragover');
            });

            mediaUploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFileUpload(files);
            });

            mediaInput.addEventListener('change', function() {
                handleFileUpload(this.files);
            });
        }

        // Hashtag suggestions
        if (hashtagInput && hashtagDropdown) {
            hashtagInput.addEventListener('input', function() {
                const value = this.value;
                if (value.includes('#') && value.length > 1) {
                    hashtagDropdown.classList.add('active');
                } else {
                    hashtagDropdown.classList.remove('active');
                }
            });

            // Handle hashtag selection
            hashtagDropdown.addEventListener('click', function(e) {
                const hashtagItem = e.target.closest('.hashtag-item');
                if (hashtagItem) {
                    const hashtagName = hashtagItem.querySelector('.hashtag-name').textContent;
                    const currentValue = hashtagInput.value;
                    const lastHashIndex = currentValue.lastIndexOf('#');
                    const newValue = currentValue.substring(0, lastHashIndex) + hashtagName + ' ';
                    hashtagInput.value = newValue;
                    hashtagDropdown.classList.remove('active');
                    hashtagInput.focus();
                }
            });
        }

        // Post options
        const postOptions = document.querySelectorAll('.post-option');
        postOptions.forEach(option => {
            option.addEventListener('click', function() {
                this.classList.toggle('active');
                const optionType = this.dataset.option;
                console.log(`${optionType} option toggled`);
            });
        });
    }

    // Handle file upload
    function handleFileUpload(files) {
        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
                console.log('File uploaded:', file.name);
                // Here you would typically upload to your server
                // For now, just show a success message
                showNotification('File uploaded successfully!', 'success');
            } else {
                showNotification('Please upload only images or videos', 'error');
            }
        });
    }

    // Enhanced feed interactions
    function initializeFeedInteractions() {
        const likeButtons = document.querySelectorAll('.feed-action-btn');
        likeButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                if (this.querySelector('svg').getAttribute('viewBox') === '0 0 24 24' &&
                    this.querySelector('path').getAttribute('d').includes('20.84 4.61')) {
                    // This is a like button
                    this.classList.toggle('liked');
                    const heartIcon = this.querySelector('svg');
                    heartIcon.classList.add('heart-animation');

                    setTimeout(() => {
                        heartIcon.classList.remove('heart-animation');
                    }, 600);

                    // Update like count
                    const countSpan = this.querySelector('span');
                    if (countSpan) {
                        let count = parseInt(countSpan.textContent);
                        countSpan.textContent = this.classList.contains('liked') ? count + 1 : count - 1;
                    }
                }
            });
        });
    }

    // Simple notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-background);
            backdrop-filter: var(--glass-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 16px 20px;
            color: var(--text-primary);
            z-index: 10000;
            animation: slideInFromRight 0.3s ease-out;
            max-width: 300px;
        `;

        if (type === 'success') {
            notification.style.borderColor = 'var(--success-color)';
        } else if (type === 'error') {
            notification.style.borderColor = 'var(--error-color)';
        }

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    </script>

    <!-- Floating Action Button -->
    <button class="fab" id="fabCreatePost" aria-label="Create new post">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
    </button>

    <!-- Enhanced Post Creation Modal -->
    <div class="post-creation-modal" id="postCreationModal">
        <div class="post-creation-content">
            <div class="post-creation-header">
                <h3>Create New Post</h3>
                <button class="close-modal-btn" id="closePostModal">&times;</button>
            </div>

            <form id="postCreationForm">
                <div class="form-group">
                    <textarea class="rich-text-editor" id="postContent" placeholder="What's your positive story today? Share your experience with the community..." rows="4"></textarea>
                </div>

                <div class="media-upload-area" id="mediaUploadArea">
                    <div class="upload-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21,15 16,10 5,21"></polyline>
                        </svg>
                    </div>
                    <div class="upload-text">Click to upload or drag and drop</div>
                    <div class="upload-subtext">PNG, JPG, GIF up to 10MB</div>
                    <input type="file" id="mediaInput" accept="image/*,video/*" multiple style="display: none;">
                </div>

                <div class="post-options">
                    <div class="post-option" data-option="location">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                        <span>Add Location</span>
                    </div>
                    <div class="post-option" data-option="feeling">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                            <line x1="9" y1="9" x2="9.01" y2="9"></line>
                            <line x1="15" y1="9" x2="15.01" y2="9"></line>
                        </svg>
                        <span>Feeling/Activity</span>
                    </div>
                    <div class="post-option" data-option="tag">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                            <line x1="7" y1="7" x2="7.01" y2="7"></line>
                        </svg>
                        <span>Tag People</span>
                    </div>
                </div>

                <div class="hashtag-suggestions">
                    <input type="text" id="hashtagInput" placeholder="Add hashtags... #BlackExcellence #Community" style="width: 100%; padding: 12px; background: var(--surface-color); border: 1px solid var(--border-color); border-radius: var(--border-radius-lg); color: var(--text-primary); margin-bottom: 16px;">
                    <div class="hashtag-dropdown" id="hashtagDropdown">
                        <div class="hashtag-item">
                            <span class="hashtag-name">#BlackExcellence</span>
                            <span class="hashtag-count">1.2k posts</span>
                        </div>
                        <div class="hashtag-item">
                            <span class="hashtag-name">#CommunityLove</span>
                            <span class="hashtag-count">856 posts</span>
                        </div>
                        <div class="hashtag-item">
                            <span class="hashtag-name">#Inspiration</span>
                            <span class="hashtag-count">2.1k posts</span>
                        </div>
                    </div>
                </div>

                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button type="button" class="btn-glass" id="saveDraftBtn">Save Draft</button>
                    <button type="submit" class="btn-glass" style="background: var(--gradient-primary); color: white; border: none;">Share Post</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Generic Modal Structure -->
    <div id="action-modal" class="modal-overlay" aria-hidden="true">
        <div class="modal-content" role="dialog" aria-modal="true">
            <div id="modal-body-content">
                <!-- Dynamic content will be loaded here by JavaScript -->
            </div>
            <button class="modal-close-button" aria-label="Close modal">&times;</button>
        </div>
    </div>
</body>
</html>
