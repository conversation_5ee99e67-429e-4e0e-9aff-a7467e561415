/* Naroop Layout Improvements - 2025 Modern Standards */

/* ===== CONTAINER SPACING SYSTEM ===== */

/* Feed Content Container - Proper spacing */
.feed-content {
    display: grid;
    gap: var(--gap-card);
    padding: var(--spacing-lg);
}

/* Content Sections - Consistent spacing */
.content-section {
    display: grid;
    gap: var(--gap-component);
    margin-bottom: var(--gap-section);
}

/* ===== CARD LAYOUT IMPROVEMENTS ===== */

/* Universal card container spacing */
.cards-container,
.posts-container,
#postsContainer {
    display: grid;
    gap: var(--gap-card);
    width: 100%;
}

/* Feature cards grid - Landing page */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--gap-section);
    margin-top: var(--gap-section);
}

/* ===== Z-INDEX MANAGEMENT ===== */

/* Header and navigation */
.header {
    z-index: var(--z-fixed);
}

.mobile-nav {
    z-index: var(--z-fixed);
}

/* Sidebar and trending */
.sidebar {
    z-index: var(--z-sticky);
}

.trending {
    z-index: var(--z-sticky);
}

/* Modals and overlays */
.modal-backdrop {
    z-index: var(--z-modal-backdrop);
}

.modal,
.auth-modal {
    z-index: var(--z-modal);
}

/* Dropdowns and popovers */
.dropdown-menu,
.user-menu {
    z-index: var(--z-dropdown);
}

/* ===== PREVENT LAYOUT SHIFTS ===== */

/* Ensure hover effects don't cause shifts */
.feed-card,
.card,
.nav-item {
    transform-origin: center;
    will-change: transform;
}

/* Smooth transitions without layout impact */
.feed-card:hover,
.card:hover {
    transform: translateY(-4px);
    /* Use transform instead of margin/padding changes */
}

/* ===== RESPONSIVE SPACING IMPROVEMENTS ===== */

/* Extra small devices - Optimized touch targets */
@media (max-width: 575.98px) {
    .cards-container,
    #postsContainer {
        gap: var(--spacing-md);
    }
    
    .feed-content {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }
    
    /* Ensure minimum touch target size (44px) */
    .feed-action-btn,
    .nav-item,
    .mobile-nav-item {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Small devices - Balanced spacing */
@media (min-width: 576px) and (max-width: 767.98px) {
    .cards-container,
    #postsContainer {
        gap: var(--gap-card);
    }
    
    .feed-content {
        padding: var(--spacing-lg);
    }
}

/* Medium devices and up - Full spacing */
@media (min-width: 768px) {
    .cards-container,
    #postsContainer {
        gap: var(--gap-card);
    }
    
    .main-content {
        gap: var(--gap-section);
    }
}

/* ===== CONTENT OVERFLOW HANDLING ===== */

/* Prevent horizontal overflow */
.feed-card-content,
.post-content {
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
}

/* Long content handling */
.feed-card-content p,
.post-content p {
    max-width: 100%;
    overflow: hidden;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Focus management for overlapping prevention */
.focus-ring:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    z-index: var(--z-dropdown);
}

/* Ensure interactive elements don't overlap */
.interactive-element {
    position: relative;
    z-index: var(--z-base);
}

.interactive-element:focus,
.interactive-element:hover {
    z-index: var(--z-dropdown);
}

/* ===== MODERN CSS FEATURES ===== */

/* Container queries for responsive cards (when supported) */
@supports (container-type: inline-size) {
    .cards-container {
        container-type: inline-size;
    }
    
    @container (max-width: 400px) {
        .feed-card {
            padding: var(--spacing-md);
        }
    }
}

/* Logical properties for better internationalization */
.card-padding {
    padding-block: var(--spacing-lg);
    padding-inline: var(--spacing-lg);
}

.card-margin {
    margin-block-end: var(--gap-card);
}

/* ===== SCROLL BEHAVIOR IMPROVEMENTS ===== */

/* Smooth scrolling without layout shifts */
.main-content {
    scroll-behavior: smooth;
}

/* Prevent scroll-triggered layout shifts */
.sticky-element {
    contain: layout style paint;
}

/* ===== GRID FALLBACKS ===== */

/* Flexbox fallback for older browsers */
@supports not (display: grid) {
    .cards-container,
    #postsContainer {
        display: flex;
        flex-direction: column;
    }
    
    .feed-card,
    .post {
        margin-bottom: var(--gap-card);
    }
    
    .feed-card:last-child,
    .post:last-child {
        margin-bottom: 0;
    }
}
