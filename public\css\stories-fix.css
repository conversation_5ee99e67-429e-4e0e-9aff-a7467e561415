/* Naroop Stories Section - Overlap Fix */

/* ===== STORIES CONTAINER FIXES ===== */

/* Stories section - Prevent overlap with trending */
.stories-section {
    position: relative;
    z-index: var(--z-base);
    background: var(--background-color);
    margin-bottom: var(--gap-card);
    width: 100%;
    overflow: hidden;
}

/* Stories container - Horizontal scroll without overlap */
.stories-container {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    position: relative;
    z-index: var(--z-base);
    width: 100%;
    box-sizing: border-box;
}

.stories-container::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

/* Story items - Proper sizing */
.story-item {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: background-color var(--transition-fast);
    min-width: 80px;
}

.story-item:hover {
    background-color: var(--surface-color);
}

/* Story avatars - Consistent sizing */
.story-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 3px solid;
    padding: 2px;
    box-sizing: border-box;
}

.story-avatar > div {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

/* Add story button */
.add-story {
    border-color: var(--border-color);
    background: var(--surface-color);
}

.add-story:hover {
    border-color: var(--primary-color);
}

/* Story labels */
.story-item span {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    text-align: center;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* ===== FEED LAYOUT IMPROVEMENTS ===== */

/* Feed sections - Clear separation */
.feed.content-section {
    position: relative;
    z-index: var(--z-base);
    background: var(--background-color);
    margin-bottom: var(--gap-section);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

/* Feed header - Proper positioning */
.feed .feed-header {
    position: relative;
    z-index: var(--z-base);
    background: var(--surface-color);
    border-bottom: 1px solid var(--divider-color);
    padding: var(--spacing-lg);
}

/* Feed content - Prevent overlap */
.feed .feed-content {
    position: relative;
    z-index: var(--z-base);
    background: var(--card-background);
    padding: var(--spacing-lg);
}

/* ===== RESPONSIVE STORIES ===== */

/* Mobile stories adjustments */
@media (max-width: 575.98px) {
    .stories-container {
        padding: var(--spacing-md);
        gap: var(--spacing-sm);
    }
    
    .story-item {
        min-width: 70px;
        padding: var(--spacing-xs);
    }
    
    .story-avatar {
        width: 56px;
        height: 56px;
    }
    
    .story-item span {
        font-size: var(--font-size-xs);
        max-width: 70px;
    }
}

/* Tablet stories adjustments */
@media (min-width: 576px) and (max-width: 767.98px) {
    .stories-container {
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
    }
    
    .story-item {
        min-width: 75px;
    }
    
    .story-avatar {
        width: 60px;
        height: 60px;
    }
}

/* ===== SCROLL BEHAVIOR FOR STORIES ===== */

/* Smooth horizontal scrolling */
.stories-container {
    scroll-behavior: smooth;
}

/* Scroll indicators (optional) */
.stories-scroll-indicator {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: var(--z-dropdown);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.stories-section:hover .stories-scroll-indicator {
    opacity: 1;
}

.stories-scroll-left {
    left: var(--spacing-sm);
}

.stories-scroll-right {
    right: var(--spacing-sm);
}

/* ===== SECTION BOUNDARIES ===== */

/* Clear visual separation between sections */
.content-section {
    position: relative;
    z-index: var(--z-base);
    background: var(--background-color);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--gap-section);
    overflow: hidden;
}

/* Section headers */
.content-section .feed-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--divider-color);
    position: relative;
    z-index: var(--z-base);
}

/* Section content */
.content-section .feed-content {
    background: var(--card-background);
    position: relative;
    z-index: var(--z-base);
}

/* ===== PREVENT HORIZONTAL OVERFLOW ===== */

/* Ensure stories don't break layout */
.main-content {
    overflow-x: hidden;
}

.feed {
    overflow-x: hidden;
}

/* Container constraints */
.stories-container,
.feed-content,
.story-prompt {
    max-width: 100%;
    box-sizing: border-box;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Focus management for stories */
.story-item:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    background-color: var(--surface-color);
}

/* Keyboard navigation */
.story-item[tabindex="0"] {
    cursor: pointer;
}

/* Screen reader improvements */
.story-item[aria-label] {
    position: relative;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* GPU acceleration for smooth scrolling */
.stories-container {
    transform: translateZ(0);
    will-change: scroll-position;
}

/* Optimize story items */
.story-item {
    transform: translateZ(0);
    will-change: transform;
}

/* Contain layout calculations */
.stories-section {
    contain: layout style paint;
}
